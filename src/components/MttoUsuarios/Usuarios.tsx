import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableColumn,
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { TableInputType } from "../../enums";
import { mockData } from "./usuariosMockData";

type CompaniaProps = {};

const URL_USER = "v1/services/treasury/user-maintenance";

const Usuarios: FC<CompaniaProps> = () => {

  const mockColumns: TableColumn[] = [
    {
      id: "table-head-1",
      label: "ID USUARIO",
      accessor: "TCME_COD_MODULO",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-2",
      label: "NOMBRE",
      accessor: "TCME_ID_NIVEL",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-3",
      label: "FECHA DESDE",
      accessor: "TCME_NODO_MAESTRO",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-4",
      label: "FECHA HASTA",
      accessor: "TCME_NODO_DEPENDIENTE",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-5",
      label: "ROL",
      accessor: "TCME_DESCRIPCION",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-6",
      label: "NUMERO DE EMPLEADO",
      accessor: "TCME_COD_MENU",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.DATE,
    },
    {
      id: "table-head-7",
      label: "STATUS",
      accessor: "TCME_COG_SUB_MENU",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    /*{
      id: "table-head-8",
      label: "ESTADO",
      accessor: "TCME_NOM_COMPONENTE",
      isLink: false,
      sortable: true,
      type: TableInputType.DATE,
    },
    {
      id: "table-head-9",
      label: "NOMBRE OBJETO FISICO PANTALLA",
      accessor: "TCME_VER_COMPONENTE",
      isLink: false,
      sortable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-10",
      label: "FECHA MOD",
      accessor: "TCIA_FEC_MOD",
      isLink: false,
      sortable: true,
      type: TableInputType.DATE,
    },*/
  ];

  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);
      
    const { result: responsePost } = usePost(callPostAPI);
    const { result, loading, error, refetch } = useGet<TableResponse>({
      url: URL_USER,
      call: true,
    });
    const onSaveUser = (newElement: TableData) => {
      setCallPostAPI({
        url: URL_USER,
        call: true,
        data: newElement,
      });
    };
    const onDeleteBanco = (editElement: TableData) => {
      setCallPostAPI({
        url: `${URL_USER}`,
        call: true,
        data: editElement,
        method: "DELETE",
      });
    };

    useEffect(() => {
      if (responsePost != null) {
        refetch();
        setCallPostAPI(initialPostApi);
      }
    }, [responsePost, refetch]);
  
    if (loading)
      return (
        <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
      );
    if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE USUARIOS</h1>
      <Table
        id="menu"
        data={result?.data ?? []}
        columns={mockColumns}
        filters={["tusuCdgUsuario", "tusuNomUsuario"]}
        onSave={onSaveUser}
        //onEdit={onEditUser}
      />
    </div>
  );
};

export default Usuarios;
