import { FC } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData } from "./mttoConceptMockData";

type MttoConceptProps = {};

const MttoConcept: FC<MttoConceptProps> = () => {
  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE CONCEPTOS</h1>
      <Table
        id="concepts"
        data={mockData}
        columns={mockColumns}
        filters={["company", "area", "concept"]}
      />
    </div>
  );
};

export default MttoConcept;
