const fs = require("fs");
const path = require("path");

const aliasMap = {
  "@components": "components",
  "@redux": "redux",
  "@hooks": "hooks",
};

const validExtensions = [".ts", ".tsx", ".js", ".jsx"];

function replaceAliasesInFile(filePath) {
  let content = fs.readFileSync(filePath, "utf8");
  let original = content;

  Object.entries(aliasMap).forEach(([alias, replacement]) => {
    const regex = new RegExp(`(['"])${alias}`, "g");
    content = content.replace(regex, `$1${replacement}`);
  });

  if (content !== original) {
    fs.writeFileSync(filePath, content, "utf8");
    console.log(`✔ Reemplazado: ${filePath}`);
  }
}

function walk(dir) {
  fs.readdirSync(dir).forEach((file) => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      walk(fullPath);
    } else if (validExtensions.includes(path.extname(fullPath))) {
      replaceAliasesInFile(fullPath);
    }
  });
}

// Ejecuta sobre src/
walk(path.join(__dirname, "src"));
