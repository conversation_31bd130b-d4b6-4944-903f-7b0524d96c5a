import { TableData } from "../MetlifeComponents/Table/table-types";

const mockData: TableData[] = [
  {
    TCME_COD_MODULO: "e1976432",
    TCME_ID_NIVEL: "Usuario01",
    TCME_NODO_MAESTRO: "14/01/2016",
    TCME_NODO_DEPENDIENTE: "05/04/2016",
    TCME_DESCRIPCION: "CASHCONF",
    TCME_ACTIVO: "1976432",
    TCME_COD_MENU: "ACTIVO",
    TCME_COG_SUB_MENU: null,
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "1",
  },
  {
    TCME_COD_MODULO: "e1976433",
    TCME_ID_NIVEL: "Usuario02",
    TCME_NODO_MAESTRO: "14/09/2018",
    TCME_NODO_DEPENDIENTE: "05/04/2018",
    TCME_DESCRIPCION: "CASHAPP",
    TCME_ACTIVO: "1976432",
    TCME_COD_MENU: "ACTIVO",
    TCME_COG_SUB_MENU: null,
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "2",
  },
  {
    TCME_COD_MODULO: "e1976434",
    TCME_ID_NIVEL: "Usuario03",
    TCME_NODO_MAESTRO: "19/04/2017",
    TCME_NODO_DEPENDIENTE: "05/04/2017",
    TCME_DESCRIPCION: "CASHADM",
    TCME_ACTIVO: "1976432",
    TCME_COD_MENU: "ACTIVO",
    TCME_COG_SUB_MENU: null,
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "3",
  },
  /*{
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "300",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Ingresos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Ingresos",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "4",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "400",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Procesos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Procesos",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "5",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "2",
    TCME_NODO_MAESTRO: "500",
    TCME_NODO_DEPENDIENTE: "0",
    TCME_DESCRIPCION: "Reportes",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Reportes",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "6",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "110",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Claves",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Claves",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "7",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "120",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Plantilla contables",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Plantilla contables",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "8",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "130",
    TCME_NODO_DEPENDIENTE: "100",
    TCME_DESCRIPCION: "Tipos de cambio",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Tipos de cambio",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "9",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "210",
    TCME_NODO_DEPENDIENTE: "200",
    TCME_DESCRIPCION: "Monitoreo de Cajas",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Monitoreo_Cajas",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "10",
  },
  {
    TCME_COD_MODULO: "I",
    TCME_ID_NIVEL: "3",
    TCME_NODO_MAESTRO: "220",
    TCME_NODO_DEPENDIENTE: "200",
    TCME_DESCRIPCION: "Consulta de Movimientos Históricos",
    TCME_ACTIVO: "S",
    TCME_COD_MENU: "MENU1",
    TCME_COG_SUB_MENU: "Consul_Mov_Hist",
    TCME_NOM_COMPONENTE: null,
    TCME_VER_COMPONENTE: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "11",
  },*/
];

export { mockData };
