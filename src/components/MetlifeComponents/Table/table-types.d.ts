/* TABLE TYPES */

import { DropDownOption } from "../DropDown/DropDownOption";

type TableDataExtraProps = {
  id?: string;
  "aria-label": string;
  "data-tooltip": string;
};

export type TableData = {
  [key: string]: string | number | boolean | TableDataExtraProps | null;
};

export type TableColumn = {
  id: string;
  label: string;
  accessor: string;
  isLink: boolean;
  sortable: boolean;
  hidden: boolean;
  editable?: boolean;
  showInNew?: boolean;
  type: TableInputType | null;
  maxLength?: number;
  displayOptions?: DropDownOption[];
  isTotal?: boolean;
};

export interface TableResponse {
  data: TableData[];
  columns: TableColumn[];
}
