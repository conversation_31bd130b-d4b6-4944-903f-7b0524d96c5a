import { useCallback, useEffect, useState } from "react";

type UseFetchResponse<T> = {
  result: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => void;
};

type GetProps = {
  method: "GET" | "HEAD";
  data?: never;
};

type PostProps = {
  method?: "POST" | "DELETE" | "PUT";
  data?: any;
};

type OptionsProps = GetProps | PostProps;

type BaseProps = { url: string; call: boolean };

type UseFetchProps = BaseProps & OptionsProps;

export type UsePostProps = BaseProps & PostProps;

const URL_PATH =
  "https://wbalxs14316e1dv0101.ase14316e1dv0101.appserviceenvironment.net/";

export function useFetch<T>({
  url,
  call,
  data,
  method,
}: UseFetchProps): UseFetchResponse<T> {
  const [loading, setLoading] = useState(call);
  const [result, setResult] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const headers = data ? { "Content-type": "application/json" } : undefined;
      const response = await fetch(URL_PATH + url, {
        headers,
        method,
        body: data ? JSON.stringify(data) : undefined,
      });
      const json: any = await response.json();
      setResult(json);
    } catch (error) {
      setError(error as Error);
    } finally {
      setLoading(false);
    }
  }, [url, data, method]);

  useEffect(() => {
    if (call) fetchData();
  }, [call, fetchData]);

  return { result, loading, error, refetch: fetchData };
}

export function useGet<T>({ url, call }: BaseProps): UseFetchResponse<T> {
  return useFetch({ url, call, method: "GET" });
}

export function usePost<T>({
  url,
  call,
  data,
  method = "POST",
}: UsePostProps): UseFetchResponse<T> {
  return useFetch({ url, call, method, data });
}

export const initialPostApi = {
  url: "",
  call: false,
  data: undefined,
  method: undefined,
};
export async function uploadFile<T>(
  endpoint: string,
  file: File
): Promise<{ result: T | null; error: string | null }> {
  const formData = new FormData();
  formData.append("file", file);

  try {
    const response = await fetch(`${URL_PATH}${endpoint}`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const message = await response.text();
      throw new Error(message || `Error ${response.status}`);
    }

    const result = await response.text();
    return { result: result as T, error: null };
  } catch (err: any) {
    return { result: null, error: err.message || "Error al subir archivo" };
  }
}
