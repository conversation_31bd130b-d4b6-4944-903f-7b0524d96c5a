import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";

type CompaniaProps = {};

const URL_COMPANY = "v1/services/treasury/catalogs/companies";

const Compania: FC<CompaniaProps> = () => {
  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);

  const { result: responsePost } = usePost(callPostAPI);
  const { result, loading, error, refetch } = useGet<TableResponse>({
    url: URL_COMPANY,
    call: true,
  });

  const onSaveCompania = (newElement: TableData) => {
    delete newElement.id;
    setCallPostAPI({
      url: URL_COMPANY,
      call: true,
      data: newElement,
    });
  };

  const onEditCompania = (editElement: TableData) => {
    setCallPostAPI({
      url: `${URL_COMPANY}/${editElement?.id}`,
      call: true,
      method: "PUT",
      data: editElement,
    });
  };

  const onDeleteCompania = (elementId: number) => {
    setCallPostAPI({
      url: `${URL_COMPANY}/${elementId}`,
      call: true,
      method: "DELETE",
    });
  };

  useEffect(() => {
    if (responsePost != null) {
      refetch();
      setCallPostAPI(initialPostApi);
    }
  }, [responsePost, refetch]);

  if (loading)
    return (
      <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
    );
  if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE COMPAÑIAS</h1>
      <Table
        id="companias"
        data={result?.data ?? []}
        columns={result?.columns ?? []}
        filters={["id", "tcia_NOMBRE", "tcia_RFC"]}
        onSave={onSaveCompania}
        onEdit={onEditCompania}
        onDelete={onDeleteCompania}
      />
    </div>
  );
};

export default Compania;
