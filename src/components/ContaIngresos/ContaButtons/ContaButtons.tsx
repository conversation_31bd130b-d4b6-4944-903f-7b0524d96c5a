import React from "react";
import "./ContaButtons.css";
import Button from "../../MetlifeComponents/Button/Button";
import {
  closeModal,
  openModal,
  setAccount,
  setBank,
} from "../.././../redux/modalBuscarSlice";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";

type ContaButtonsProps = {};
const ContaButtons: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  return (
    <div className="button-wrapper">
      <Button
        text="Buscar"
        funcionC={() => {
          dispatch(openModal());
        }}
        style={undefined}
      ></Button>
      ,
      <Button
        text="Procesar"
        funcionC={() => {
          /*setShowModal(true);
                      //navigate("consulta"); */
        }}
        style={undefined}
      ></Button>
      ,
    </div>
  );
};
export default ContaButtons;
