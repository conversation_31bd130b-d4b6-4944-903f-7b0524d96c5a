import { FC, useState } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData, accountingEntryOptions } from "./mttoAccTemplateMockData";
import { TableData } from "../MetlifeComponents/Table/table-types";
import Modal from "../Modal/Modal";
import Button from "../MetlifeComponents/Button/Button";
import MLDCReactDropdown from "@metlife/mldc-react-dropdown";
import MLDCReactInputNumber from "@metlife/mldc-react-input-number";

type MttoAccTemplateProps = {};

const MttoAccTemplate: FC<MttoAccTemplateProps> = () => {
  const [data, setData] = useState<TableData[]>(mockData);
  const [showSuccessModal, setShowSuccessModal] = useState<boolean>(false);
  const [showSecondaryForm, setShowSecondaryForm] = useState<boolean>(false);
  const [currentElement, setCurrentElement] = useState<TableData | null>(null);
  const [secondaryFormData, setSecondaryFormData] = useState<Partial<TableData>>({});
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [showEditSuccessModal, setShowEditSuccessModal] = useState<boolean>(false);

  const handleSave = (element: TableData) => {
    // Generate new ID
    const newId = (data.length + 1).toString();

    // Create complete element with default values for non-required fields
    const newElement: TableData = {
      id: newId,
      company: element.company || "",
      area: element.area || "",
      module: element.module || "",
      concept: element.concept || "",
      templateId: element.templateId || 0,
      description: element.description || "",
      movementType: element.movementType || "",
      // Default values for non-required fields
      accountingEntry: "",
      account: 0,
      subAccount: 0,
      subSubAccount: 0,
      auxiliar1: 0,
      auxiliar2: 0,
      sixthLevel: 0,
      deleteActionExtraProps: {
        "aria-label": "Borrar",
        "data-tooltip": "Borrar",
      },
      editActionExtraProps: {
        id: `edit-action-${newId}`,
        "aria-label": "Editar",
        "data-tooltip": "Editar",
      },
    };

    // Add new element at the beginning of the list
    setData([newElement, ...data]);

    // Store current element for secondary form
    setCurrentElement(newElement);

    // Show success modal
    setShowSuccessModal(true);
  };

  const handleEdit = (element: TableData) => {
    // Find the current element from the data array to get all its current values
    const currentElementFromData = data.find(item => item.id === element.id);
    const elementToEdit = currentElementFromData || element;

    // Update the element in the data array with the new values from the first form
    const updatedElement = { ...elementToEdit, ...element };
    setData(prevData =>
      prevData.map(item =>
        item.id === element.id ? updatedElement : item
      )
    );

    // Store current element for secondary form (with all current data)
    setCurrentElement(updatedElement);

    // Pre-populate secondary form data with existing values from the current element
    setSecondaryFormData({
      accountingEntry: elementToEdit.accountingEntry || "",
      account: elementToEdit.account !== undefined && elementToEdit.account !== null ? Number(elementToEdit.account) : undefined,
      subAccount: elementToEdit.subAccount !== undefined && elementToEdit.subAccount !== null ? Number(elementToEdit.subAccount) : undefined,
      subSubAccount: elementToEdit.subSubAccount !== undefined && elementToEdit.subSubAccount !== null ? Number(elementToEdit.subSubAccount) : undefined,
      auxiliar1: elementToEdit.auxiliar1 !== undefined && elementToEdit.auxiliar1 !== null ? Number(elementToEdit.auxiliar1) : undefined,
      auxiliar2: elementToEdit.auxiliar2 !== undefined && elementToEdit.auxiliar2 !== null ? Number(elementToEdit.auxiliar2) : undefined,
      sixthLevel: elementToEdit.sixthLevel !== undefined && elementToEdit.sixthLevel !== null ? Number(elementToEdit.sixthLevel) : undefined,
    });

    // Set edit mode and show success modal
    setIsEditMode(true);
    setShowEditSuccessModal(true);
  };

  const handleDelete = (elementId: number) => {
    console.log("Element deleted with ID:", elementId);
  };

  const handleCloseModal = () => {
    setShowSuccessModal(false);
    // Open secondary form after closing success modal
    setShowSecondaryForm(true);
  };

  const handleCloseEditModal = () => {
    setShowEditSuccessModal(false);

    // When opening secondary form in edit mode, get the current complete data
    if (isEditMode && currentElement) {
      // Find the current element in the data array to get all its current values
      const currentCompleteElement = data.find(item => item.id === currentElement.id);

      if (currentCompleteElement) {
        // Pre-populate secondary form data with existing values from the current element
        setSecondaryFormData({
          accountingEntry: currentCompleteElement.accountingEntry?.toString() || "",
          account: currentCompleteElement.account !== undefined && currentCompleteElement.account !== null ? Number(currentCompleteElement.account) : undefined,
          subAccount: currentCompleteElement.subAccount !== undefined && currentCompleteElement.subAccount !== null ? Number(currentCompleteElement.subAccount) : undefined,
          subSubAccount: currentCompleteElement.subSubAccount !== undefined && currentCompleteElement.subSubAccount !== null ? Number(currentCompleteElement.subSubAccount) : undefined,
          auxiliar1: currentCompleteElement.auxiliar1 !== undefined && currentCompleteElement.auxiliar1 !== null ? Number(currentCompleteElement.auxiliar1) : undefined,
          auxiliar2: currentCompleteElement.auxiliar2 !== undefined && currentCompleteElement.auxiliar2 !== null ? Number(currentCompleteElement.auxiliar2) : undefined,
          sixthLevel: currentCompleteElement.sixthLevel !== undefined && currentCompleteElement.sixthLevel !== null ? Number(currentCompleteElement.sixthLevel) : undefined,
        });
      }
    }

    // Open secondary form after closing edit success modal
    setShowSecondaryForm(true);
  };

  const handleSecondaryFormChange = (field: string, value: any) => {
    setSecondaryFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateSecondaryForm = () => {
    const requiredFields = [
      { field: 'accountingEntry', name: 'Asiento contable' },
      { field: 'account', name: 'Cuenta' },
      { field: 'subAccount', name: 'SubCuenta' },
      { field: 'subSubAccount', name: 'SubSCuenta' },
      { field: 'auxiliar1', name: 'Auxiliar 1' },
      { field: 'auxiliar2', name: 'Auxiliar 2' },
      { field: 'sixthLevel', name: 'Sexto Nivel' }
    ];

    const missingFields = requiredFields.filter(({ field }) => {
      const value = secondaryFormData[field];
      if (field === 'accountingEntry') {
        // For dropdown, check if value is selected
        return !value || value === "";
      } else {
        // For numeric fields, check if value is defined and is a valid number
        if (isEditMode) {
          // In edit mode, allow 0 values but require the field to be defined
          return value === undefined || value === "" || isNaN(Number(value));
        } else {
          // In create mode, require values greater than 0
          return !value || value === "" || Number(value) <= 0;
        }
      }
    });

    return { isValid: missingFields.length === 0, missingFields };
  };

  const handleSecondaryFormSave = () => {
    // Validate all fields are filled
    const validation = validateSecondaryForm();
    if (!validation.isValid) {
      const missingFieldNames = validation.missingFields.map(f => f.name).join(', ');
      alert(`Por favor, complete los siguientes campos obligatorios: ${missingFieldNames}`);
      return;
    }

    if (currentElement) {
      // Update the element with secondary form data
      const updatedElement: TableData = {
        ...currentElement,
        accountingEntry: secondaryFormData.accountingEntry || "",
        account: secondaryFormData.account || 0,
        subAccount: secondaryFormData.subAccount || 0,
        subSubAccount: secondaryFormData.subSubAccount || 0,
        auxiliar1: secondaryFormData.auxiliar1 || 0,
        auxiliar2: secondaryFormData.auxiliar2 || 0,
        sixthLevel: secondaryFormData.sixthLevel || 0,
      };

      if (isEditMode) {
        // Update existing element in edit mode
        setData(prevData =>
          prevData.map(item =>
            item.id === currentElement.id ? updatedElement : item
          )
        );
      } else {
        // Update new element in create mode
        setData(prevData =>
          prevData.map(item =>
            item.id === currentElement.id ? updatedElement : item
          )
        );
      }
    }

    // Close secondary form and reset states
    setShowSecondaryForm(false);
    setCurrentElement(null);
    setSecondaryFormData({});
    setIsEditMode(false);
  };

  const handleSecondaryFormExit = () => {
    setShowSecondaryForm(false);
    setCurrentElement(null);
    setSecondaryFormData({});
    setIsEditMode(false);
  };

  return (
    <>
      <div className="container">
        <h1 className="title">MANTENIMIENTO DE PLANTILLAS CONTABLES</h1>
        <Table
          id="accTemplates"
          data={data}
          columns={mockColumns}
          filters={[
            "company",
            "area",
            "description",
            "concept",
            "movementType",
            "module",
            "templateId",
          ]}
          onSave={handleSave}
          onEdit={handleEdit}
          onDelete={handleDelete}
          enableAddRow={true}
        />
      </div>

      <Modal
        isOpen={showSuccessModal}
        title="Éxito"
        onClose={handleCloseModal}
        onAcept={handleCloseModal}
      >
        <div style={{ textAlign: "center", padding: "20px" }}>
          <p style={{ fontSize: "18px", marginBottom: "20px" }}>
            Plantilla contable anexada
          </p>
          <Button
            text="Aceptar"
            funcionC={handleCloseModal}
          />
        </div>
      </Modal>

      <Modal
        isOpen={showEditSuccessModal}
        title="Éxito"
        onClose={handleCloseEditModal}
        onAcept={handleCloseEditModal}
      >
        <div style={{ textAlign: "center", padding: "20px" }}>
          <p style={{ fontSize: "18px", marginBottom: "20px" }}>
            Plantilla contable actualizada
          </p>
          <Button
            text="Aceptar"
            funcionC={handleCloseEditModal}
          />
        </div>
      </Modal>

      <Modal
        isOpen={showSecondaryForm}
        title={isEditMode ? "Editar Datos Adicionales" : "Datos Adicionales"}
        onClose={handleSecondaryFormExit}
        onAcept={handleSecondaryFormSave}
      >
        <div style={{ padding: "20px" }}>
          <div style={{ marginBottom: "15px", padding: "10px", backgroundColor: "#f8f9fa", borderRadius: "5px", border: "1px solid #dee2e6" }}>
            <p style={{ margin: "0", fontSize: "14px", color: "#6c757d" }}>
              <span style={{ color: "red" }}>*</span> Todos los campos son obligatorios.
              {isEditMode
                ? " Los campos numéricos deben ser números válidos (se permiten valores 0)."
                : " Los campos numéricos deben ser mayores a 0."
              }
            </p>
          </div>
          <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "20px", marginBottom: "20px" }}>
            <div>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                Asiento contable <span style={{ color: "red" }}>*</span>
              </label>
              <MLDCReactDropdown
                id="accountingEntry"
                label=""
                placeholder="Seleccionar asiento"
                options={accountingEntryOptions}
                selectedValue={secondaryFormData.accountingEntry?.toString() || ""}
                onChange={(value: any) => {
                  const selectedValue = typeof value === 'string' ? value : value?.value || value?.target?.value || '';
                  handleSecondaryFormChange("accountingEntry", selectedValue);
                }}
                fullWidth={true}
              />
            </div>
            <div>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                Cuenta <span style={{ color: "red" }}>*</span>
              </label>
              <MLDCReactInputNumber
                id="account"
                label=""
                value={secondaryFormData.account !== undefined ? Number(secondaryFormData.account) : undefined}
                onChange={(value: any) => {
                  const numericValue = typeof value === 'number' ? value : Number(value?.target?.value || value || 0);
                  handleSecondaryFormChange("account", numericValue);
                }}
                fullWidth={true}
              />
            </div>
            <div>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                SubCuenta <span style={{ color: "red" }}>*</span>
              </label>
              <MLDCReactInputNumber
                id="subAccount"
                label=""
                value={secondaryFormData.subAccount !== undefined ? Number(secondaryFormData.subAccount) : undefined}
                onChange={(value: any) => {
                  const numericValue = typeof value === 'number' ? value : Number(value?.target?.value || value || 0);
                  handleSecondaryFormChange("subAccount", numericValue);
                }}
                fullWidth={true}
              />
            </div>
            <div>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                SubSCuenta <span style={{ color: "red" }}>*</span>
              </label>
              <MLDCReactInputNumber
                id="subSubAccount"
                label=""
                value={secondaryFormData.subSubAccount !== undefined ? Number(secondaryFormData.subSubAccount) : undefined}
                onChange={(value: any) => {
                  const numericValue = typeof value === 'number' ? value : Number(value?.target?.value || value || 0);
                  handleSecondaryFormChange("subSubAccount", numericValue);
                }}
                fullWidth={true}
              />
            </div>
            <div>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                Auxiliar 1 <span style={{ color: "red" }}>*</span>
              </label>
              <MLDCReactInputNumber
                id="auxiliar1"
                label=""
                value={secondaryFormData.auxiliar1 !== undefined ? Number(secondaryFormData.auxiliar1) : undefined}
                onChange={(value: any) => {
                  const numericValue = typeof value === 'number' ? value : Number(value?.target?.value || value || 0);
                  handleSecondaryFormChange("auxiliar1", numericValue);
                }}
                fullWidth={true}
              />
            </div>
            <div>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                Auxiliar 2 <span style={{ color: "red" }}>*</span>
              </label>
              <MLDCReactInputNumber
                id="auxiliar2"
                label=""
                value={secondaryFormData.auxiliar2 !== undefined ? Number(secondaryFormData.auxiliar2) : undefined}
                onChange={(value: any) => {
                  const numericValue = typeof value === 'number' ? value : Number(value?.target?.value || value || 0);
                  handleSecondaryFormChange("auxiliar2", numericValue);
                }}
                fullWidth={true}
              />
            </div>
            <div>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold" }}>
                Sexto Nivel <span style={{ color: "red" }}>*</span>
              </label>
              <MLDCReactInputNumber
                id="sixthLevel"
                label=""
                value={secondaryFormData.sixthLevel !== undefined ? Number(secondaryFormData.sixthLevel) : undefined}
                onChange={(value: any) => {
                  const numericValue = typeof value === 'number' ? value : Number(value?.target?.value || value || 0);
                  handleSecondaryFormChange("sixthLevel", numericValue);
                }}
                fullWidth={true}
              />
            </div>
          </div>
          <div style={{ display: "flex", justifyContent: "flex-end", gap: "10px", marginTop: "20px" }}>
            <Button
              text="Guardar"
              funcionC={handleSecondaryFormSave}
            />
            <Button
              text="Salir"
              funcionC={handleSecondaryFormExit}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default MttoAccTemplate;
