import { FC, useState } from "react";
import "./Estado.css";

import EstadoButtons from "./EstadoButtons/EstadoButtons";
import EstadoDropDowns from "./EstadoDropDowns/EstadoDropDowns";
import ConsultarModal from "./ConsultarModal/ConsultarModal";
type EstadoProps = {};

const Estado = () => {
  return (
    <div className="estado-page">
      <h6 className="title">Mantenimiento de Estados de Cuenta</h6>
      <div className="estado-content">
        <EstadoDropDowns></EstadoDropDowns>
      </div>
      <div className="button-wrapper">
        <EstadoButtons></EstadoButtons>
      </div>
      <ConsultarModal></ConsultarModal>
    </div>
  );
};

export default Estado;
