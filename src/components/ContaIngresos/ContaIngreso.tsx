import { FC, useState } from "react";
import "./ContaIngreso.css";
import { useNavigate } from "react-router-dom";
import DropDown from "../MetlifeComponents/DropDown/DropDown";
import DatePicker from "../MetlifeComponents/DatePicker/DatePicker";
import Button from "../MetlifeComponents/Button/Button";
import Modal from "../Estados/Modal/Modal";
import EstadoButtons from "../Estados/EstadoButtons/EstadoButtons";
import ContaButtons from "./ContaButtons/ContaButtons";
import ContaDropBox from "./ContaDropBox/ContaDropBox";
import ContaDatePicker from "./ContaDatePicker/ContaDatePicker";
import ConsultarModal from "./BuscarModal/BuscarModal";

type ContaIngresosProps = {};

const ContaIngreso: FC<ContaIngresosProps> = () => {
  const navigate = useNavigate();

  const [showModal, setShowModal] = useState(false);
  const [fechaInicial, setFechaInicial] = useState("");
  const [fechaFinal, setFechaFinal] = useState("");
  return (
    <div className="conta-content">
      <h6 className="title">Contabilidad de ingresos</h6>
      <div className="dropdown-container">
        <ContaDropBox></ContaDropBox>
        <ContaDatePicker></ContaDatePicker>
      </div>

      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",

          gap: "2rem",
          paddingLeft: "490px",
          width: "225.29px",
          height: "50px",
          marginTop: "1rem",
        }}
      ></div>

      <div className="button-wrapper">
        <ContaButtons></ContaButtons>
        <ConsultarModal></ConsultarModal>
      </div>
    </div>
  );
};

export default ContaIngreso;
function setBancoSeleccionado(value: string) {
  throw new Error("Function not implemented.");
}
