import React from "react";
import "./Modal.css";
import Button from "../MetlifeComponents/Button/Button";
type ModalProps = {
  isOpen: boolean;
  title?: string;
  onClose: () => void;
  onAcept: () => void;
  children: React.ReactNode;
};
const Modal: React.FC<ModalProps> = ({
  isOpen,
  title,
  onClose,
  children,
  onAcept,
}) => {
  const btnSalirStyle = { display: "flex" };
  if (!isOpen) return null;
  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>{title || "Modal"}</h2>
        </div>
        <div className="modal-body">{children}</div>
      </div>
    </div>
  );
};
export default Modal;
