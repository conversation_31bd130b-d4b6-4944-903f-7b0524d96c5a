import DatePicker from "../../../components/MetlifeComponents/DatePicker/DatePicker";
import React, { FC } from "react";
type CotaDatePicker = {};
const ContaDatePicker: React.FC = () => {
  const setFechaInicial = (value: any) => {};
  const setFinalDate = (value: any) => {};
  const initialDate: any = "";
  const finalDate: any = "";

  return (
    <div className="right-inputs">
      <DatePicker
        id="fecha-inicial"
        label="FECHA DE MOVIMIENTOS"
        value={initialDate}
        onChange={(e) => setFechaInicial(e.target.value)}
        disabled={false}
        className=""
        style={{ width: "180px" }}
      ></DatePicker>
      <DatePicker
        id="fecha-final"
        label="FECHA CONTABLE"
        value={finalDate}
        onChange={(e) => setFinalDate(e.target.value)}
        disabled={false}
        className=""
        style={{ width: "180px" }}
      ></DatePicker>
    </div>
  );
};
export default ContaDatePicker;
