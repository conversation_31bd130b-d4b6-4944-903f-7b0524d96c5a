import { createSlice } from "@reduxjs/toolkit";
const initialState = {
  isOpen: false,
  isOpen2: false,
  bank: "",
  account: "",
  operationDate: "",
  company: "",
  policyType: "",
  contablePolicy: "",
};
const modalBuscarSlice = createSlice({
  name: "buscarModal",
  initialState,
  reducers: {
    openModal: (state) => {
      state.isOpen = true;
    },

    closeModal: (state) => {
      state.isOpen = false;
    },

    setBank: (state, action) => {
      state.bank = action.payload;
    },
    setAccount: (state, action) => {
      state.account = action.payload;
    },
    setOperationDate: (state, action) => {
      state.operationDate = action.payload;
    },
    setContablePolicy: (state, action) => {
      state.contablePolicy = action.payload;
    },
    setPolicyType: (state, action) => {
      state.policyType = action.payload;
    },
    setIsOpen: (state, action) => {
      state.isOpen = action.payload;
    },
    setCompany: (state, action) => {
      state.company = action.payload;
    },
  },
});
export const {
  openModal,
  closeModal,
  setBank,
  setAccount,

  setOperationDate,
  setContablePolicy,
  setPolicyType,
  setIsOpen,
  setCompany,
} = modalBuscarSlice.actions;
export default modalBuscarSlice.reducer;
