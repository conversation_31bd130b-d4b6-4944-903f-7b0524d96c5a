import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import "./BuscarModal.css";

import { RootState } from "../.././../redux/store";
import {
  closeModal,
  openModal,
  setContablePolicy,
  setOperationDate,
  setBank,
  setPolicyType,
  setCompany,
} from "../.././../redux/modalBuscarSlice";

import { useNavigate } from "react-router-dom";
import Modal from "components/Modal/Modal";
import DropDown from "components/MetlifeComponents/DropDown/DropDown";
import { DropDownOption } from "components/MetlifeComponents/DropDown/DropDownOption";
import DatePicker from "../../MetlifeComponents/DatePicker/DatePicker";
import Button from "../../MetlifeComponents/Button/Button";
type ConsultarModalProps = {};
const ConsultarModal: React.FC<ConsultarModalProps> = ({}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const FileOnUpload = () => {};
  const isOpenState = useSelector(
    (state: RootState) => state.modalBuscar.isOpen
  );
  const companyStateValue = useSelector(
    (state: RootState) => state.modalBuscar.company
  );
  const PolicyTypeStateValue = useSelector(
    (state: RootState) => state.modalBuscar.policyType
  );
  const contablePolicyStateValue = useSelector(
    (state: RootState) => state.modalBuscar.contablePolicy
  );
  const opDate = useSelector(
    (state: RootState) => state.modalBuscar.operationDate
  );
  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = () => {};

  const DropDownCompanyHandleOnChange = (company: DropDownOption) => {
    dispatch(setCompany(company.value));
  };
  const DropDownPolicyTypeHandleOnChange = (type: DropDownOption) => {
    dispatch(setPolicyType(type.value));
  };
  const ContablePolicyOnchangeHandler = (
    cP: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setContablePolicy(cP.target.value));
  };
  const operationDateOnChangeHandler = (
    operationDate: React.ChangeEvent<HTMLInputElement>
  ) => {
    dispatch(setOperationDate(operationDate.target.value));
  };

  const DropDownBancosStyle = {
    width: "900px",
    height: "50px",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    width: "225.29x",
    height: "50px",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "Compañia1",
      label: "Compañia1",
      value: "Compañia1",
    },
    {
      ariaLabel: "Compañia2",
      label: "Compañia2",
      value: "Compañia2",
    },
    {
      ariaLabel: "Compañia3",
      label: "Compañia3",
      value: "Compañia3",
    },
  ];

  const DropDownTipoOptions = [
    {
      ariaLabel: "Ingresos-I",
      label: "I-Ingresos",
      value: "I",
    },
    {
      ariaLabel: "E-Engresos",
      label: "E-Egresos",
      value: "E",
    },
  ];
  const handleonClickSalir = () => {
    dispatch(closeModal());
  };

  return (
    <div className="consultar-modal-container">
      <Modal
        isOpen={isOpenState}
        title="Buscar"
        onClose={() => {}}
        onAcept={() => {}}
      >
        <div className="companies">
          <label>Compañia</label>
          <DropDown
            opts={DropDownBancosOptions}
            change={DropDownCompanyHandleOnChange}
            click={() => {}}
            placeholder="Compañia"
            selectedValue={companyStateValue}
            disabled={false}
            style={{ width: "100%" }}
          ></DropDown>
        </div>
        <div className="controls">
          <label>Póliza Contable</label>
          <input
            type="text"
            style={{ height: "60px", borderRadius: "5px", fontSize: "18px" }}
            placeholder="Póliza Contable"
            value={contablePolicyStateValue}
            onChange={ContablePolicyOnchangeHandler}
          ></input>
          <label>Tipo de Póliza</label>
          <DropDown
            opts={DropDownTipoOptions}
            change={DropDownPolicyTypeHandleOnChange}
            click={() => {}}
            placeholder="Tipo de Póliza"
            selectedValue={PolicyTypeStateValue}
            disabled={false}
            style={{ width: "180px" }}
          ></DropDown>
        </div>
        <div className="operation">
          <label>Fecha de Operación</label>
          <DatePicker
            id={"operation-date"}
            value={opDate}
            onChange={operationDateOnChangeHandler}
            style={{ width: "200px" }}
          ></DatePicker>
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "1rem",
            marginTop: "1.51rem",
          }}
        >
          <Button text="Procesar"></Button>
          <Button text="Salir" funcionC={handleonClickSalir}></Button>
        </div>
      </Modal>
    </div>
  );
};
export default ConsultarModal;
