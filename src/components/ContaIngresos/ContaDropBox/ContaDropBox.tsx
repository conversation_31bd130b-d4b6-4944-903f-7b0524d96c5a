import "./ContaDropBox.css";
import React, { useState } from "react";
import DropDown from "../../MetlifeComponents/DropDown/DropDown";
import {
  closeModal,
  openModal,
  setAccount,
  setBank,
} from "../.././../redux/modalBuscarSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@redux/store";
import { DropDownOption } from "@components/MetlifeComponents/DropDown/DropDownOption";
type ContaDropBoxProps = {};
const ContaDropBox: React.FC<ContaDropBoxProps> = () => {
  const [BancoSeleccionado, setBancoSeleccionado] = useState("");
  const dispatch = useDispatch();
  const DropDownBancosStyle = {
    maxWidth: "300px",
    width: "100%",
    marginTop: "1rem",
  };
  const DropDownCuentasStyle = {
    maxWidth: "300px",
    width: "100%",
    marginTop: "0.5rem",
  };

  const DropDownBancosOptions = [
    {
      ariaLabel: "BBVA",
      label: "BBVA",
      value: "BBVA",
    },
    {
      ariaLabel: "City",
      label: "City",
      value: "City",
    },
    {
      ariaLabel: "HSBC",
      label: "HSBC",
      value: "HSBC",
    },
    {
      ariaLabel: "JPMorgan",
      label: "JPMorgan",
      value: "JPMorgan",
    },
  ];
  const optionsR = [
    {
      label: "Formato Tradicional",
      value: "tradicional",
    },
    {
      label: "MT490",
      value: "MT490",
    },
  ];
  const DropDownCuentasOptions = [
    {
      ariaLabel: "1234",
      label: "1234",
      value: "1234",
    },
    {
      ariaLabel: "**********",
      label: "**********",
      value: "**********",
    },
    {
      ariaLabel: "4*********",
      label: "*********",
      value: "*********",
    },
  ];
  const DropDownCuentasHandleOnchange = () => {};
  const handleUpload = (file: File) => {};
  const DropDownBancosHandleClick = () => {};
  const DropDownCuentasHandleClick = (acc: DropDownOption) => {
    dispatch(setAccount(acc.value));
  };
  const DropDownBancosHandleOnChange = (bank: DropDownOption) => {
    dispatch(setBank(bank.value));
  };
  const bancoState = useSelector((state: RootState) => state.modalBuscar.bank);
  const cuentaState = useSelector(
    (state: RootState) => state.modalBuscar.account
  );
  return (
    <div className="left-inputs">
      <div className="form-inline-bancos">
        <label htmlFor=" bancos"> Banco:</label>
        <DropDown
          change={DropDownBancosHandleOnChange}
          click={DropDownBancosHandleClick}
          opts={DropDownBancosOptions}
          style={DropDownBancosStyle}
          placeholder="Seleccione Banco"
          disabled={false}
          selectedValue={bancoState}
        ></DropDown>
      </div>
      <div className="form-inline-cuentas">
        <label htmlFor="cuentas"> Cuenta:</label>
        <DropDown
          change={DropDownCuentasHandleOnchange}
          click={DropDownBancosHandleClick}
          style={DropDownCuentasStyle}
          opts={DropDownCuentasOptions}
          placeholder="Seleccione Cuenta"
          selectedValue={cuentaState}
          disabled={bancoState === ""}
        ></DropDown>
      </div>
    </div>
  );
};
export default ContaDropBox;
